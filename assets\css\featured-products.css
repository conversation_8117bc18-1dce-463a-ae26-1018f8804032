/**
 * Featured Products Section - Professional Arabic RTL E-commerce Design
 * Modern, clean, and responsive styling for featured products
 */

/* ==========================================================================
   Featured Products Section Container
   ========================================================================== */

.featured-products-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.featured-products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
    z-index: 1;
}

.featured-products-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

/* ==========================================================================
   Section Header
   ========================================================================== */

.featured-products-header {
    text-align: center;
    margin-bottom: 4rem;
    padding: 2rem 0;
}

.featured-products-title {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #2c3e50 0%, #667eea 50%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 8px rgba(44, 62, 80, 0.1);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.featured-products-subtitle {
    font-size: 1.3rem;
    color: #6c757d;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    opacity: 0.9;
}

/* ==========================================================================
   Products Grid
   ========================================================================== */

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-bottom: 4rem;
}

/* ==========================================================================
   Professional Product Cards
   ========================================================================== */

.professional-product-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 
        0 20px 40px rgba(0,0,0,0.1),
        0 0 0 1px rgba(255,255,255,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
    transform-style: preserve-3d;
}

.professional-product-card:hover {
    transform: translateY(-20px) rotateX(5deg) rotateY(-5deg) scale(1.03);
    box-shadow: 
        0 40px 80px rgba(0,0,0,0.2),
        0 0 0 1px rgba(255,255,255,0.4),
        inset 0 1px 0 rgba(255,255,255,0.5);
}

.professional-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.1) 0%, 
        rgba(118, 75, 162, 0.1) 50%, 
        rgba(240, 147, 251, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    border-radius: 24px;
}

.professional-product-card:hover::before {
    opacity: 1;
}

/* ==========================================================================
   Product Image Container
   ========================================================================== */

.product-image-container {
    position: relative;
    width: 100%;
    height: 320px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 24px 24px 0 0;
}

.product-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    z-index: 1;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    z-index: 2;
}

.professional-product-card:hover .product-image {
    transform: scale(1.1) rotate(2deg);
    filter: brightness(1.1) contrast(1.05);
}

/* ==========================================================================
   Discount Badge
   ========================================================================== */

.professional-discount-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 700;
    z-index: 10;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
    transform: rotate(-5deg);
    transition: all 0.3s ease;
}

.professional-product-card:hover .professional-discount-badge {
    transform: rotate(0deg) scale(1.1);
    box-shadow: 0 12px 35px rgba(220, 53, 69, 0.5);
}

/* ==========================================================================
   Card Content
   ========================================================================== */

.professional-card-content {
    padding: 2.5rem;
    position: relative;
    z-index: 3;
}

.product-category {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-description {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ==========================================================================
   Price Section
   ========================================================================== */

.professional-price-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.professional-price-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: transform 0.6s ease;
}

.professional-product-card:hover .professional-price-section::before {
    transform: translateX(100%);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.current-price {
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    letter-spacing: -0.02em;
}

.original-price {
    font-size: 1.1rem;
    color: #6c757d;
    text-decoration: line-through;
    opacity: 0.8;
    font-weight: 500;
}

/* ==========================================================================
   Action Buttons
   ========================================================================== */

.professional-card-actions {
    display: flex;
    gap: 1rem;
    position: relative;
    z-index: 3;
}

.professional-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.professional-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: transform 0.6s ease;
}

.professional-btn:hover::before {
    transform: translateX(200%);
}

.btn-view {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

.btn-view:hover {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(108, 117, 125, 0.4);
    color: white;
}

.btn-cart {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-cart:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    color: white;
}

/* ==========================================================================
   Section Footer
   ========================================================================== */

.featured-products-footer {
    text-align: center;
    padding: 2rem 0;
}

.btn-view-all {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 3rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-decoration: none;
    border-radius: 60px;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    overflow: hidden;
}

.btn-view-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: transform 0.6s ease;
}

.btn-view-all:hover::before {
    transform: translateX(200%);
}

.btn-view-all:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 45px rgba(40, 167, 69, 0.4);
    color: white;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Tablets */
@media (max-width: 768px) {
    .featured-products-section {
        padding: 4rem 0;
    }

    .featured-products-container {
        padding: 0 1rem;
    }

    .featured-products-header {
        margin-bottom: 3.5rem;
        padding: 1.5rem 0;
    }

    .featured-products-title {
        font-size: 2.8rem;
        margin-bottom: 1rem;
    }

    .featured-products-subtitle {
        font-size: 1.2rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
    }

    .professional-product-card {
        border-radius: 20px;
    }

    .professional-product-card:hover {
        transform: translateY(-15px) scale(1.02);
    }

    .product-image-container {
        height: 280px;
    }

    .professional-card-content {
        padding: 2rem;
    }

    .product-title {
        font-size: 1.4rem;
    }
}

/* Mobile Phones */
@media (max-width: 576px) {
    .featured-products-section {
        padding: 3rem 0;
    }

    .featured-products-container {
        padding: 0 1rem;
    }

    .featured-products-header {
        margin-bottom: 3rem;
        padding: 1rem 0;
    }

    .featured-products-title {
        font-size: 2.2rem;
        margin-bottom: 0.75rem;
        line-height: 1.2;
    }

    .featured-products-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .professional-product-card {
        border-radius: 18px;
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    .professional-product-card:hover {
        transform: translateY(-10px) scale(1.01);
    }

    .product-image-container {
        height: 250px;
    }

    .professional-card-content {
        padding: 1.75rem;
    }

    .product-category {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        margin-bottom: 0.75rem;
    }

    .product-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .product-description {
        font-size: 0.95rem;
        -webkit-line-clamp: 2;
        margin-bottom: 1.5rem;
    }

    .professional-price-section {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .current-price {
        font-size: 1.6rem;
    }

    .original-price {
        font-size: 1rem;
    }

    .professional-card-actions {
        gap: 0.75rem;
    }

    .professional-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        border-radius: 40px;
    }

    .btn-view-all {
        padding: 1rem 2.5rem;
        font-size: 1rem;
        border-radius: 50px;
    }

    .professional-discount-badge {
        top: 1rem;
        right: 1rem;
        padding: 0.5rem 0.875rem;
        font-size: 0.8rem;
    }
}

/* ==========================================================================
   Accessibility & Performance Enhancements
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .professional-product-card,
    .product-image,
    .professional-btn,
    .btn-view-all,
    .professional-discount-badge {
        transition: none !important;
        animation: none !important;
    }

    .professional-product-card:hover {
        transform: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .professional-product-card {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .professional-btn {
        border: 2px solid #000;
    }

    .featured-products-title {
        text-shadow: none;
        color: #000;
    }
}

/* Loading states */
.product-image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Focus states for keyboard navigation */
.professional-btn:focus,
.btn-view-all:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .featured-products-section {
        background: white !important;
        box-shadow: none !important;
    }

    .professional-product-card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        break-inside: avoid;
    }

    .professional-btn,
    .btn-view-all {
        display: none !important;
    }
}
