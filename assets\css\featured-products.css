/**
 * Ultra-Professional Featured Products Section
 * Completely Redesigned for Maximum Visual Appeal and User Engagement
 *
 * @version 2.0
 * <AUTHOR> Development Team
 */

/* ==========================================================================
   Ultra-Professional Section Container
   ========================================================================== */

.featured-products-section {
    position: relative;
    padding: 5rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    margin: 0;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.featured-products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: 1;
}

.featured-products-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(1px);
    z-index: 2;
}

.featured-products-container {
    position: relative;
    z-index: 3;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ==========================================================================
   Ultra-Professional Section Header
   ========================================================================== */

.featured-products-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
    padding: 2rem 0;
}

.featured-products-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.featured-products-header::after {
    content: '';
    position: absolute;
    bottom: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 6px;
    background: linear-gradient(90deg, transparent 0%, #ffffff 20%, #ffffff 80%, transparent 100%);
    border-radius: 3px;
    box-shadow: 0 0 20px rgba(255,255,255,0.5);
}

.featured-products-title {
    font-size: 3.5rem;
    font-weight: 900;
    color: #ffffff;
    margin-bottom: 1.5rem;
    text-shadow:
        0 0 10px rgba(255,255,255,0.3),
        0 0 20px rgba(255,255,255,0.2),
        0 0 30px rgba(255,255,255,0.1);
    position: relative;
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.featured-products-subtitle {
    font-size: 1.4rem;
    color: rgba(255,255,255,0.9);
    font-weight: 300;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* ==========================================================================
   Ultra-Professional Product Grid
   ========================================================================== */

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
    margin-bottom: 4rem;
    perspective: 1000px;
}

/* ==========================================================================
   Ultra-Professional Product Cards
   ========================================================================== */

.professional-product-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.1),
        0 0 0 1px rgba(255,255,255,0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
    transform-style: preserve-3d;
}

.professional-product-card:hover {
    transform: translateY(-20px) rotateX(5deg) rotateY(-5deg) scale(1.03);
    box-shadow:
        0 40px 80px rgba(0,0,0,0.2),
        0 0 0 1px rgba(255,255,255,0.4),
        inset 0 1px 0 rgba(255,255,255,0.5);
}

.professional-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 50%,
        rgba(240, 147, 251, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    border-radius: 24px;
}

.professional-product-card:hover::before {
    opacity: 1;
}

.professional-product-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
    z-index: 2;
    pointer-events: none;
}

.professional-product-card:hover::after {
    transform: rotate(45deg) translate(50%, 50%);
}

/* ==========================================================================
   Ultra-Professional Image Container
   ========================================================================== */

.product-image-container {
    position: relative;
    width: 100%;
    height: 320px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 50%, #f8f9fa 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 24px 24px 0 0;
}

.product-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    z-index: 1;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    padding: 1.5rem;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.1));
}

.professional-product-card:hover .product-image {
    transform: scale(1.15) rotateY(5deg);
    filter: drop-shadow(0 20px 40px rgba(0,0,0,0.2));
}

.product-image-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(to top, rgba(255,255,255,0.8) 0%, transparent 100%);
    z-index: 3;
}

/* Ultra-Professional Image Loading States */
.product-image-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(102, 126, 234, 0.1) 25%,
        rgba(118, 75, 162, 0.1) 50%,
        rgba(240, 147, 251, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmerPro 2s ease-in-out infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
    border-radius: 24px 24px 0 0;
}

@keyframes shimmerPro {
    0% {
        background-position: 200% 0;
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        background-position: -200% 0;
        opacity: 0.6;
    }
}

.image-placeholder {
    color: #667eea;
    font-size: 4rem;
    opacity: 0.4;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.4; }
    50% { transform: scale(1.1); opacity: 0.6; }
}

.product-image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    padding: 2rem;
}

.product-image-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
    color: #764ba2;
}

/* ==========================================================================
   Ultra-Professional Discount Badge
   ========================================================================== */

.professional-discount-badge {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff3838 100%);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 800;
    z-index: 5;
    box-shadow:
        0 8px 25px rgba(255, 107, 107, 0.4),
        0 0 0 3px rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: rotate(-8deg);
    animation: discountPulse 3s ease-in-out infinite;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 0.5px;
}

@keyframes discountPulse {
    0%, 100% {
        transform: rotate(-8deg) scale(1);
        box-shadow:
            0 8px 25px rgba(255, 107, 107, 0.4),
            0 0 0 3px rgba(255, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
        transform: rotate(-8deg) scale(1.1);
        box-shadow:
            0 12px 35px rgba(255, 107, 107, 0.6),
            0 0 0 5px rgba(255, 255, 255, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}

.professional-discount-badge::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24, #ff3838);
    border-radius: 50px;
    z-index: -1;
    filter: blur(4px);
    opacity: 0.7;
}

/* ==========================================================================
   Ultra-Professional Card Content
   ========================================================================== */

.professional-card-content {
    padding: 2.5rem;
    position: relative;
    z-index: 3;
    background: linear-gradient(to bottom, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.95) 100%);
    backdrop-filter: blur(10px);
}

.product-category {
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.product-category::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.professional-product-card:hover .product-category::before {
    left: 100%;
}

.product-category i {
    font-size: 1rem;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
}

.product-title {
    font-size: 1.6rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 1.25rem;
    line-height: 1.25;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    letter-spacing: -0.01em;
}

.product-description {
    color: #5a6c7d;
    font-size: 1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 400;
}

/* ==========================================================================
   Ultra-Professional Price Section
   ========================================================================== */

.professional-price-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 50%,
        rgba(240, 147, 251, 0.1) 100%);
    border-radius: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.professional-price-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.professional-product-card:hover .professional-price-section::before {
    transform: translateX(100%);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.current-price {
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
    letter-spacing: -0.02em;
}

.original-price {
    font-size: 1.1rem;
    color: #6c757d;
    text-decoration: line-through;
    opacity: 0.8;
    font-weight: 500;
}

.discount-percentage {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 0.5px;
}

/* ==========================================================================
   Ultra-Professional Action Buttons
   ========================================================================== */

.professional-card-actions {
    display: flex;
    gap: 1rem;
    margin-top: auto;
}

.professional-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.6rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.professional-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.professional-btn:hover::before {
    left: 100%;
}

.professional-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
}

.professional-btn:active::after {
    width: 300px;
    height: 300px;
}

.btn-view {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
}

.btn-view:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-add-cart {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: 2px solid rgba(255,255,255,0.2);
}

.btn-add-cart:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4);
    color: white;
}

.btn-add-cart:active {
    transform: translateY(-2px) scale(0.98);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
}

.professional-btn i {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.professional-btn:hover i {
    transform: scale(1.2);
}

/* ==========================================================================
   Ultra-Professional View All Products Button
   ========================================================================== */

.view-all-container {
    text-align: center;
    margin-top: 4rem;
    position: relative;
}

.view-all-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 1;
}

.btn-view-all {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    animation: gradientMove 4s ease infinite;
    color: white;
    text-decoration: none;
    border-radius: 60px;
    font-size: 1.2rem;
    font-weight: 800;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow:
        0 15px 40px rgba(102, 126, 234, 0.3),
        0 0 0 3px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.btn-view-all::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.8s ease;
}

.btn-view-all:hover::before {
    left: 100%;
}

.btn-view-all::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn-view-all:hover::after {
    width: 400px;
    height: 400px;
}

.btn-view-all:hover {
    transform: translateY(-8px) scale(1.08);
    box-shadow:
        0 25px 60px rgba(102, 126, 234, 0.5),
        0 0 0 5px rgba(255, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    color: white;
}

.btn-view-all i {
    font-size: 1.4rem;
    transition: transform 0.4s ease;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.btn-view-all:hover i {
    transform: scale(1.3) rotate(360deg);
}

/* ==========================================================================
   Ultra-Professional Loading States
   ========================================================================== */

.products-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    flex-direction: column;
    gap: 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 24px;
    margin: 2rem 0;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 5px solid rgba(102, 126, 234, 0.2);
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spinPro 1.2s cubic-bezier(0.23, 1, 0.32, 1) infinite;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

@keyframes spinPro {
    0% {
        transform: rotate(0deg);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
    }
    100% {
        transform: rotate(360deg);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
    }
}

.loading-text {
    color: #667eea;
    font-size: 1.2rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* ==========================================================================
   Ultra-Professional Empty State
   ========================================================================== */

.products-empty {
    text-align: center;
    padding: 5rem 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 24px;
    margin: 2rem 0;
}

.products-empty i {
    font-size: 5rem;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    opacity: 0.7;
}

.products-empty h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    font-weight: 700;
}

.products-empty p {
    font-size: 1.1rem;
    max-width: 500px;
    margin: 0 auto;
    color: #5a6c7d;
    line-height: 1.6;
}

/* ==========================================================================
   Ultra-Professional Responsive Design - Mobile First Approach
   ========================================================================== */

/* Large Tablets and Small Desktops */
@media (max-width: 1200px) {
    .featured-products-container {
        padding: 0 1.5rem;
        max-width: 1100px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
    }

    .featured-products-title {
        font-size: 3rem;
    }

    .featured-products-subtitle {
        font-size: 1.3rem;
    }

    .product-image-container {
        height: 300px;
    }
}

/* Tablets - Enhanced Responsive Design */
@media (max-width: 768px) {
    .featured-products-section {
        padding: 4rem 0;
        margin: 1.5rem 0;
    }

    .featured-products-container {
        padding: 0 1rem;
    }

    .featured-products-header {
        margin-bottom: 3.5rem;
        padding: 1.5rem 0;
    }

    .featured-products-title {
        font-size: 2.8rem;
        margin-bottom: 1rem;
    }

    .featured-products-subtitle {
        font-size: 1.2rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
    }

    .professional-product-card {
        border-radius: 20px;
    }

    .professional-product-card:hover {
        transform: translateY(-15px) scale(1.02);
    }

    .product-image-container {
        height: 280px;
    }

    .professional-card-content {
        padding: 2rem;
    }

    .product-title {
        font-size: 1.4rem;
    }

    .professional-card-actions {
        gap: 0.75rem;
    }

    .professional-btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .btn-view-all {
        padding: 1.25rem 3rem;
        font-size: 1.1rem;
    }

    .professional-discount-badge {
        top: 1.25rem;
        right: 1.25rem;
        padding: 0.625rem 1rem;
        font-size: 0.85rem;
    }
}

/* Mobile Phones - Ultra-Professional Mobile Experience */
@media (max-width: 576px) {
    .featured-products-section {
        padding: 3rem 0;
        margin: 1rem 0;
    }

    .featured-products-container {
        padding: 0 1rem;
    }

    .featured-products-header {
        margin-bottom: 3rem;
        padding: 1rem 0;
    }

    .featured-products-title {
        font-size: 2.2rem;
        margin-bottom: 0.75rem;
        line-height: 1.2;
    }

    .featured-products-subtitle {
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .professional-product-card {
        border-radius: 18px;
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    .professional-product-card:hover {
        transform: translateY(-10px) scale(1.01);
    }

    .product-image-container {
        height: 250px;
    }

    .professional-card-content {
        padding: 1.75rem;
    }

    .product-category {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        margin-bottom: 0.75rem;
    }

    .product-title {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .product-description {
        font-size: 0.95rem;
        -webkit-line-clamp: 2;
        margin-bottom: 1.5rem;
    }

    .professional-price-section {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }

    .current-price {
        font-size: 1.6rem;
    }

    .original-price {
        font-size: 1rem;
    }

    .professional-card-actions {
        gap: 0.75rem;
    }

    .professional-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        border-radius: 40px;
    }

    .btn-view-all {
        padding: 1rem 2.5rem;
        font-size: 1rem;
        border-radius: 50px;
    }

    .professional-discount-badge {
        top: 1rem;
        right: 1rem;
        padding: 0.5rem 0.875rem;
        font-size: 0.8rem;
    }
}

/* Extra Small Devices */
@media (max-width: 400px) {
    .featured-products-title {
        font-size: 1.6rem;
    }

    .featured-products-subtitle {
        font-size: 0.95rem;
    }

    .product-image-container {
        height: 180px;
    }

    .professional-card-content {
        padding: 1rem;
    }

    .product-title {
        font-size: 1rem;
    }

    .current-price {
        font-size: 1.2rem;
    }

    .btn-view-all {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* ==========================================================================
   Accessibility & Performance
   ========================================================================== */

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .professional-product-card,
    .product-image,
    .professional-btn,
    .btn-view-all,
    .professional-discount-badge {
        transition: none !important;
        animation: none !important;
    }

    .professional-product-card:hover {
        transform: none !important;
    }

    .product-image-loading {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .professional-product-card {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .professional-btn {
        border: 2px solid #000;
    }

    .featured-products-title {
        text-shadow: none;
        color: #000;
    }
}

/* Focus styles for accessibility */
.professional-btn:focus,
.btn-view-all:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .featured-products-section {
        background: white !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }

    .professional-product-card {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        break-inside: avoid;
    }

    .professional-card-actions {
        display: none !important;
    }

    .professional-discount-badge {
        background: #000 !important;
        color: white !important;
    }
}
